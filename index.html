<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Waymo Safety Impact Dashboard</title>

    <style>
      body {
        margin: 0;
        font-family: sans-serif;
        line-height: 1.4;
        background: #f9f9f9;
      }
      .site-header {
        background: #222;
        color: #fff;
        padding: 1rem 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .site-header h1 {
        margin: 0;
        font-size: 1.75rem;
      }
      .site-header nav ul {
        list-style: none;
        margin: 0;
        padding: 0;
        display: flex;
        gap: 1rem;
      }
      .site-header nav a {
        color: #eee;
        text-decoration: none;
        font-size: 1rem;
        padding: 0.5rem 1rem;
      }
      .site-header nav a.active,
      .site-header nav a:hover {
        background: #444;
        border-radius: 4px;
      }
      .scene-header {
        text-align: center;
        padding: 1.5rem 1rem 0.5rem;
      }
      .scene-header h2 {
        margin: 0;
        font-size: 1.5rem;
      }
      .scene-header p {
        margin: 0.5rem 0 1rem;
        font-size: 1.1rem;
        color: #555;
      }
      #chart {
        padding: 1rem;
        display: flex;
        justify-content: center;
      }
      .tooltip {
        font-size: 1rem;
      }
      svg text {
        font-size: 14px;
      }
      svg .axis-label {
        font-size: 16px;
        font-weight: normal;
      }
      svg .label {
        font-size: 14px;
      }
      /* Modal overlay */
      .modal {
        display: none; /* hidden by default */
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        justify-content: center;
        align-items: center;
        z-index: 1000;
      }

      /* Modal box */
      .modal-content {
        background: #fff;
        padding: 2rem;
        max-width: 500px;
        margin: 1rem;
        border-radius: 8px;
        position: relative;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }

      /* Close button */
      .modal-close {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        font-size: 1.5rem;
        background: none;
        border: none;
        cursor: pointer;
      }
    </style>
  </head>
  <body>
    <header class="site-header">
      <h1>Waymo Safety Impact Dashboard</h1>
      <nav>
        <ul>
          <li>
            <a href="#" data-scene="collisionVsMiles" class="active"
              >Collision vs RO Miles</a
            >
          </li>
          <li>
            <a href="#" data-scene="collisionsByType">Collisions by Type</a>
          </li>
          <li><a href="#" data-scene="dimensionTrends">Dimension Trends</a></li>
        </ul>
      </nav>
    </header>

    <section class="scene-header">
      <!-- Narrative introduction -->
      <h2>Collision Count vs RO Miles by Location</h2>
      <p class="description">
        This scatter plot compares Rider-Only miles (in millions) against total
        collisions for each city. Hover over points for detailed metrics and
        click to explore collision types.
      </p>
    </section>

    <div id="chart"></div>

    <!-- Intro Modal -->
    <div id="intro-modal" class="modal">
      <div class="modal-content">
        <button id="intro-close" class="modal-close">&times;</button>
        <h2>Welcome to the Waymo Safety Impact Dashboard</h2>
        <p>
          This interactive story reveals how Waymo’s rider-only fleet performs
          on the road. We begin by comparing exposure (miles driven) to
          collision counts across cities, then drill into crash-type breakdowns,
          and finally trace safety dimensions over time. Click through the tabs
          to follow the full safety narrative.
        </p>
      </div>
    </div>

    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="main.js"></script>
    <script>
      // Scene metadata
      const sceneInfo = {
        collisionVsMiles: {
          title: 'Collision Count vs RO Miles by Location',
          description:
            'This scatter plot compares Rider-Only miles (in millions) against total collisions for each city. Hover over points for detailed metrics and click to explore crash-type breakdowns.',
        },
        collisionsByType: {
          title: 'Collisions by Crash Type',
          description:
            'This chart shows collision counts by crash type. Select one or more locations to filter; multiple locations render a stacked bar chart.',
        },
        dimensionTrends: {
          title: 'Dimension Trends Over Time',
          description:
            'Explore how different crash dimensions (outcome, type, severity) evolve over time for a selected location.',
        },
      };

      document.querySelectorAll('.site-header nav a').forEach((link) => {
        link.addEventListener('click', (e) => {
          e.preventDefault();
          const scene = link.dataset.scene;
          // update URL
          const params = new URLSearchParams();
          params.set('scene', scene);
          window.history.pushState({}, '', `?${params.toString()}`);
          // highlight active
          document
            .querySelectorAll('.site-header nav a')
            .forEach((a) => a.classList.remove('active'));
          link.classList.add('active');
          // update header
          const info = sceneInfo[scene];
          document.querySelector('.scene-header h2').textContent = info.title;
          document.querySelector('.scene-header .description').textContent =
            info.description;
          // render scene
          sceneManager.show(scene, { selector: '#chart', config: {} });
        });
      });

      // initial render
      (function () {
        const params = new URLSearchParams(window.location.search);
        const scene = params.get('scene') || 'collisionVsMiles';
        const loc = params.get('location');
        // highlight nav
        document
          .querySelectorAll('.site-header nav a')
          .forEach((a) => a.classList.remove('active'));
        const initLink = document.querySelector(
          `.site-header nav a[data-scene="${scene}"]`
        );
        if (initLink) initLink.classList.add('active');
        // update header
        const info = sceneInfo[scene];
        document.querySelector('.scene-header h2').textContent = info.title;
        document.querySelector('.scene-header .description').textContent =
          info.description;
        // build config
        const config = {};
        if (scene === 'collisionsByType' && loc) config.initialLocation = loc;
        if (scene === 'dimensionTrends') {
          if (loc) config.initialLocation = loc;
          const fromScene = params.get('fromScene');
          if (fromScene) {
            config.fromScene = fromScene;
            config.fromLocation = loc;
            config.initialDimension = 'crashType'; // Set to crashType when coming from collisionsByType
          }
        }
        // show scene
        sceneManager.show(scene, { selector: '#chart', config });
      })();
    </script>
  </body>
</html>
